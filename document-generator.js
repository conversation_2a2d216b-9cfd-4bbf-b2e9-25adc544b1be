// Document Generation Utilities for Veritas Chat AI
// Supports PDF, DOCX, CSV, and XLSX generation

class DocumentGenerator {
    constructor() {
        this.supportedFormats = ['pdf', 'docx', 'csv', 'xlsx'];
    }

    // Main method to generate documents based on format
    async generateDocument(format, content, filename = null) {
        const normalizedFormat = format.toLowerCase();
        
        if (!this.supportedFormats.includes(normalizedFormat)) {
            throw new Error(`Unsupported format: ${format}. Supported formats: ${this.supportedFormats.join(', ')}`);
        }

        const defaultFilename = filename || `document_${Date.now()}`;
        
        switch (normalizedFormat) {
            case 'pdf':
                return await this.generatePDF(content, defaultFilename);
            case 'docx':
                return await this.generateDOCX(content, defaultFilename);
            case 'csv':
                return await this.generateCSV(content, defaultFilename);
            case 'xlsx':
                return await this.generateXLSX(content, defaultFilename);
            default:
                throw new Error(`Format ${format} not implemented`);
        }
    }

    // Generate PDF document
    async generatePDF(content, filename) {
        try {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();
            
            // Set font and styling
            doc.setFont('helvetica', 'normal');
            doc.setFontSize(12);
            
            // Add title
            doc.setFontSize(16);
            doc.setFont('helvetica', 'bold');
            doc.text('Generated Document', 20, 20);
            
            // Add content with text wrapping
            doc.setFontSize(12);
            doc.setFont('helvetica', 'normal');
            
            const pageWidth = doc.internal.pageSize.getWidth();
            const margin = 20;
            const maxWidth = pageWidth - (margin * 2);
            
            // Split content into lines and handle page breaks
            const lines = doc.splitTextToSize(content, maxWidth);
            let yPosition = 40;
            const lineHeight = 7;
            const pageHeight = doc.internal.pageSize.getHeight();
            
            lines.forEach(line => {
                if (yPosition > pageHeight - margin) {
                    doc.addPage();
                    yPosition = margin;
                }
                doc.text(line, margin, yPosition);
                yPosition += lineHeight;
            });
            
            // Generate blob and create download
            const pdfBlob = doc.output('blob');
            return this.createDownloadLink(pdfBlob, `${filename}.pdf`, 'PDF Document');
            
        } catch (error) {
            console.error('PDF generation error:', error);
            throw new Error('Failed to generate PDF document');
        }
    }

    // Generate DOCX document
    async generateDOCX(content, filename) {
        try {
            const { Document, Packer, Paragraph, TextRun, HeadingLevel } = window.docx;
            
            // Create document with title and content
            const doc = new Document({
                sections: [{
                    properties: {},
                    children: [
                        new Paragraph({
                            text: "Generated Document",
                            heading: HeadingLevel.TITLE,
                        }),
                        new Paragraph({
                            children: [
                                new TextRun({
                                    text: content,
                                    size: 24, // 12pt font
                                }),
                            ],
                        }),
                    ],
                }],
            });
            
            // Generate blob
            const docxBlob = await Packer.toBlob(doc);
            return this.createDownloadLink(docxBlob, `${filename}.docx`, 'Word Document');
            
        } catch (error) {
            console.error('DOCX generation error:', error);
            throw new Error('Failed to generate DOCX document');
        }
    }

    // Generate CSV document
    async generateCSV(content, filename) {
        try {
            // Try to parse content as structured data, otherwise create simple CSV
            let csvContent = '';
            
            // Check if content looks like JSON or structured data
            try {
                const jsonData = JSON.parse(content);
                if (Array.isArray(jsonData)) {
                    csvContent = this.jsonToCSV(jsonData);
                } else {
                    csvContent = this.objectToCSV(jsonData);
                }
            } catch {
                // If not JSON, try to parse as table-like text
                csvContent = this.textToCSV(content);
            }
            
            const csvBlob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            return this.createDownloadLink(csvBlob, `${filename}.csv`, 'CSV File');
            
        } catch (error) {
            console.error('CSV generation error:', error);
            throw new Error('Failed to generate CSV document');
        }
    }

    // Generate XLSX document
    async generateXLSX(content, filename) {
        try {
            const XLSX = window.XLSX;
            let worksheetData = [];
            
            // Try to parse content as structured data
            try {
                const jsonData = JSON.parse(content);
                if (Array.isArray(jsonData)) {
                    worksheetData = jsonData;
                } else {
                    worksheetData = [jsonData];
                }
            } catch {
                // If not JSON, create simple worksheet with content
                worksheetData = [
                    ['Generated Document'],
                    ['Content:', content]
                ];
            }
            
            const worksheet = XLSX.utils.json_to_sheet(worksheetData);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
            
            const xlsxBlob = new Blob([XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });
            
            return this.createDownloadLink(xlsxBlob, `${filename}.xlsx`, 'Excel Spreadsheet');
            
        } catch (error) {
            console.error('XLSX generation error:', error);
            throw new Error('Failed to generate XLSX document');
        }
    }

    // Helper method to convert JSON array to CSV
    jsonToCSV(jsonArray) {
        if (jsonArray.length === 0) return '';
        
        const headers = Object.keys(jsonArray[0]);
        const csvRows = [headers.join(',')];
        
        jsonArray.forEach(row => {
            const values = headers.map(header => {
                const value = row[header] || '';
                return `"${String(value).replace(/"/g, '""')}"`;
            });
            csvRows.push(values.join(','));
        });
        
        return csvRows.join('\n');
    }

    // Helper method to convert object to CSV
    objectToCSV(obj) {
        const csvRows = ['Key,Value'];
        Object.entries(obj).forEach(([key, value]) => {
            csvRows.push(`"${key}","${String(value).replace(/"/g, '""')}"`);
        });
        return csvRows.join('\n');
    }

    // Helper method to convert text to CSV
    textToCSV(text) {
        const lines = text.split('\n').filter(line => line.trim());
        const csvRows = ['Content'];
        lines.forEach(line => {
            csvRows.push(`"${line.replace(/"/g, '""')}"`);
        });
        return csvRows.join('\n');
    }

    // Create download link with proper styling
    createDownloadLink(blob, filename, displayName) {
        const url = URL.createObjectURL(blob);
        const downloadId = `download_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        return {
            url: url,
            filename: filename,
            displayName: displayName,
            downloadId: downloadId,
            size: this.formatFileSize(blob.size),
            html: this.generateDownloadHTML(url, filename, displayName, downloadId, blob.size)
        };
    }

    // Generate HTML for download button
    generateDownloadHTML(url, filename, displayName, downloadId, size) {
        return `
            <div class="document-download" id="${downloadId}">
                <div class="download-info">
                    <div class="download-icon">
                        <i class="fas fa-file-download"></i>
                    </div>
                    <div class="download-details">
                        <div class="download-name">${displayName}</div>
                        <div class="download-filename">${filename}</div>
                        <div class="download-size">${this.formatFileSize(size)}</div>
                    </div>
                </div>
                <button class="download-btn" onclick="window.documentGenerator.downloadFile('${url}', '${filename}')">
                    <i class="fas fa-download"></i>
                    Download
                </button>
            </div>
        `;
    }

    // Format file size for display
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Download file method
    downloadFile(url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up the URL after download
        setTimeout(() => {
            URL.revokeObjectURL(url);
        }, 1000);
    }

    // Parse document command
    parseDocumentCommand(message) {
        const commandRegex = /^\/document\s+(\w+)\s*-\s*(.+)$/i;
        const match = message.match(commandRegex);
        
        if (match) {
            return {
                isDocumentCommand: true,
                format: match[1].toLowerCase(),
                content: match[2].trim()
            };
        }
        
        return { isDocumentCommand: false };
    }
}

// Initialize global document generator
window.documentGenerator = new DocumentGenerator();
